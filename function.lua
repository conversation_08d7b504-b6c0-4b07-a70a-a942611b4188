--[[
function.lua - 基础函数库
包含各种常用的字符串处理、数字转换、表操作等实用函数
--]]

--[[
函数列表：

=== 基础字符串和数字处理函数 ===
trans() - 中文数字转阿拉伯数字
fx_to_fangxiang() - 方向缩写转完整名称
null() - 判断变量是否为空
delete() - 删除字符串中的子字符串
change() - 替换字符串中的子字符串
trim() - 去除字符串两端空白
begins() - 检查字符串是否以指定内容开头
ends() - 检查字符串是否以指定内容结尾
right() - 获取字符串右侧部分
rightback() - 获取字符串右侧部分（包含指定位置）
left() - 获取字符串左侧部分
leftback() - 获取字符串左侧部分（从末尾计算）
pos() - 查找子字符串位置
copy() - 复制指定长度的子字符串

=== 列表和表操作函数 ===
del_item() - 从列表中删除指定项目
get_item() - 获取列表中指定位置的项目
num_item() - 计算列表中项目数量
ismember() - 检查值是否在表中
is_table_empty() - 检查表是否为空
copy_table() - 浅拷贝表
list_to_table() - 字符串列表转表
setvar() - 设置全局变量

=== 游戏相关函数 ===
carryqty() - 查询物品携带数量

=== 触发器管理函数 ===
manage_triggers() - 通用触发器管理
close_triggers() - 关闭指定前缀和范围的触发器
open_triggers() - 打开指定前缀和范围的触发器
delete_triggers() - 删除指定前缀和范围的触发器
manage_scattered_triggers() - 管理零散索引的触发器
close_scattered_triggers() - 关闭指定前缀和零散索引的触发器
open_scattered_triggers() - 打开指定前缀和零散索引的触发器
delete_scattered_triggers() - 删除指定前缀和零散索引的触发器

=== 寻路算法函数 ===
astar_search() - 通用A*寻路算法


=== 日志和输出函数 ===
log_message() - 通用日志输出函数

=== 物品购买函数 ===
buy_da_huandan() - 购买大还丹的专用函数
buy_huandan_recursive() - 递归购买大还丹的辅助函数
buy_item() - 通用物品购买函数
buy_item_recursive() - 递归购买物品的辅助函数

=== HP监控函数 ===
setup_hp_monitor() - 通用HP检测函数
remove_hp_monitor() - 移除HP监控触发器

=== 通天塔相关函数 ===
get_ttt_level_range() - 根据通天塔层数返回所在范围
set_ttt_skills_by_level() - 根据通天塔层数设置相应的技能和alias

=== 时间计算函数 ===
set_start_time() - 设置开始时间
set_end_time() - 设置结束时间
calculate_time_diff() - 计算时间差

=== 格式化和工具函数 ===
align_right() - 通用右对齐函数

=== 内存统计函数 ===
show_memory_stats() - 通用内存统计函数
--]]

--[[
函数名: trans
功能: 将中文数字转换为阿拉伯数字
参数: chinese (string) - 中文数字字符串，如"三十二"、"一千二百三十四"等
返回值: number - 转换后的阿拉伯数字，非法输入返回0
示例: trans("三十二") 返回 32
      trans("一千二百三十四") 返回 1234
算法说明:
1. 首先验证输入是否为有效的中文数字字符串
2. 将中文数字字符替换为数学表达式
3. 使用loadstring执行数学表达式得到结果
--]]
function trans(chinese)
    -- 参数类型检查：如果不是字符串类型，返回0
    if type(chinese) ~= "string" then
        chinese = nil
        return 0
        -- 空字符串检查：如果是空字符串，返回0
    elseif chinese == "" then
        chinese = nil
        return 0
    else
        -- 创建副本用于验证是否为纯中文数字
        local is_number = chinese
        -- 移除所有中文数字字符，检查是否还有其他字符
        is_number = string.gsub(is_number, "零", "")
        is_number = string.gsub(is_number, "十", "")
        is_number = string.gsub(is_number, "百", "")
        is_number = string.gsub(is_number, "千", "")
        is_number = string.gsub(is_number, "万", "")
        is_number = string.gsub(is_number, "亿", "")
        is_number = string.gsub(is_number, "一", "")
        is_number = string.gsub(is_number, "二", "")
        is_number = string.gsub(is_number, "三", "")
        is_number = string.gsub(is_number, "四", "")
        is_number = string.gsub(is_number, "五", "")
        is_number = string.gsub(is_number, "六", "")
        is_number = string.gsub(is_number, "七", "")
        is_number = string.gsub(is_number, "八", "")
        is_number = string.gsub(is_number, "九", "")
        is_number = string.gsub(is_number, " ", "")

        -- 如果移除所有中文数字字符后为空，说明是有效的中文数字
        if is_number == "" then
            is_number = nil
            local number = chinese
            -- 将中文数字转换为数学表达式
            -- 零 -> *0+（乘以0再加）
            number = string.gsub(number, "零", "*0+")
            -- 十 -> *10+（乘以10再加）
            number = string.gsub(number, "十", "*10+")
            -- 百 -> *100+（乘以100再加）
            number = string.gsub(number, "百", "*100+")
            -- 千 -> *1000+（乘以1000再加）
            number = string.gsub(number, "千", "*1000+")
            -- 万 -> 前面的数乘以10000，后面的数直接加
            number = string.gsub(number, "万", "+0)*10000+(0+")
            -- 亿 -> 前面的数乘以100000000，后面的数直接加
            number = string.gsub(number, "亿", "+0)*100000000+(0+")
            -- 将中文数字字符替换为阿拉伯数字
            number = string.gsub(number, "一", "1")
            number = string.gsub(number, "二", "2")
            number = string.gsub(number, "三", "3")
            number = string.gsub(number, "四", "4")
            number = string.gsub(number, "五", "5")
            number = string.gsub(number, "六", "6")
            number = string.gsub(number, "七", "7")
            number = string.gsub(number, "八", "8")
            number = string.gsub(number, "九", "9")
            number = string.gsub(number, " ", "")
            -- 在表达式两端加上括号和0，确保计算正确
            number = "(0+" .. number .. "+0)"
            -- 清理多余的加号
            number = string.gsub(number, "++", "+")
            number = string.gsub(number, "+%*", "+")
            -- 构造可执行的Lua代码
            number = "return " .. number
            -- 执行数学表达式并返回结果
            local trans_math = loadstring(number)
            number = trans_math()
            return number
        else
            -- 如果包含非中文数字字符，返回0
            is_number = nil
            chinese = nil
            return 0
        end
    end
end

-- 检查表中是否包含某个值
function table_contains(t, value)
    for _, v in pairs(t) do
        if v == value then
            return true
        end
    end
    return false
end

--[[
函数名: fx_to_fangxiang
功能: 将方向缩写转换为完整的方向名称
参数: dir (string) - 方向缩写，如"s"、"ne"、"eu"等
返回值: string - 完整的方向名称，如果没有对应的映射则返回原字符串
示例: fx_to_fangxiang("s") 返回 "south"
      fx_to_fangxiang("ne") 返回 "northeast"
      fx_to_fangxiang("unknown") 返回 "unknown"
用途: 主要用于游戏中的方向指令转换
--]]
function fx_to_fangxiang(dir)
    -- 方向映射表：缩写 -> 完整名称
    local fangxiang_dir = {
        -- 基本四方向
        ["e"] = "east", -- 东
        ["s"] = "south", -- 南
        ["w"] = "west", -- 西
        ["n"] = "north", -- 北
        ["u"] = "up",  -- 上
        ["d"] = "down", -- 下
        ["out"] = "out", -- 出
        ["enter"] = "enter", -- 进

        -- 斜方向（四个角）
        ["ne"] = "northeast", -- 东北
        ["se"] = "southeast", -- 东南
        ["sw"] = "southwest", -- 西南
        ["nw"] = "northwest", -- 西北

        -- 上斜方向
        ["eu"] = "eastup", -- 东上
        ["su"] = "southup", -- 南上
        ["wu"] = "westup", -- 西上
        ["nu"] = "northup", -- 北上

        -- 下斜方向
        ["ed"] = "eastdown", -- 东下
        ["sd"] = "southdown", -- 南下
        ["wd"] = "westdown", -- 西下
        ["nd"] = "northdown", -- 北下
    }

    -- 查找映射表中是否有对应的完整名称
    if fangxiang_dir[dir] then
        return fangxiang_dir[dir]
    else
        -- 如果没有找到映射，返回原字符串
        return dir
    end
end

--[[
函数名: null
功能: 判断变量是否为"空"值
参数: t (any) - 要检查的变量，可以是任意类型
返回值: boolean - 如果是"空"值返回true，否则返回false
判断规则:
- number类型：0被认为是空，非0被认为是非空
- string类型：空字符串""被认为是空，非空字符串被认为是非空
- table类型：空表{}被认为是空，有元素的表被认为是非空
- 其他类型或nil：都被认为是空
示例: null(0) 返回 true
      null("") 返回 true
      null({}) 返回 true
      null(nil) 返回 true
      null(1) 返回 false
      null("hello") 返回 false
      null({1,2,3}) 返回 false
--]]
function null(t)
    -- 检查数字类型：非0数字为非空
    if type(t) == "number" and t ~= 0 then
        return false
        -- 检查字符串类型：非空字符串为非空
    elseif type(t) == "string" and t ~= "" then
        return false
        -- 检查表类型：有元素的表为非空（使用next函数检查表是否为空）
    elseif type(t) == "table" and not (_G.next(t) == nil) then
        return false
    else
        -- 其他情况（包括nil、0、空字符串、空表等）都认为是空
        return true
    end
end

--[[
函数名: del_item
功能: 从分隔符分隔的列表中删除指定的项目
参数:
  _item (string) - 原始列表字符串，如"a|b|c|d|e"
  str (string) - 要删除的项目
  sep (string, 可选) - 分隔符，默认为"|"
返回值: string - 删除指定项目后的列表字符串
示例: del_item("a|b|c|d|e", "c", "|") 返回 "a|b|d|e"
      del_item("e;s;s;e", "s", ";") 返回 "e;e"
算法说明: 使用字符串替换技巧，先将分隔符加倍，再删除目标项，最后恢复分隔符
--]]
function del_item(_item, str, sep)
    -- 检查参数是否有效
    if _item or str then
        -- 设置默认分隔符
        sep = sep or "|"
        -- 将所有分隔符加倍，防止误删
        local new_item = string.gsub(_item, sep, sep .. sep)
        -- 在首尾添加分隔符，然后删除目标项（包括前后的分隔符）
        new_item = string.gsub(sep .. new_item .. sep, sep .. str .. sep, "")
        -- 将加倍的分隔符恢复为单个
        new_item = string.gsub(new_item, sep .. sep, sep)
        -- 去掉首尾的分隔符
        new_item = string.match(new_item, "^" .. sep .. "(.+)" .. sep .. "$")
        return new_item
    else
        -- 参数无效时返回原字符串
        return _item
    end
end

--[[
函数名: get_item
功能: 从分隔符分隔的列表中获取指定位置的项目
参数:
  _list (string) - 列表字符串，如"a|b|c|d|e"
  number (number) - 要获取的项目位置（从1开始）
  separate (string, 可选) - 分隔符，默认为"|"
返回值: string - 指定位置的项目，如果位置无效返回空字符串
示例: get_item("a|b|c|d|e", 3, "|") 返回 "c"
      get_item("apple,banana,orange", 2, ",") 返回 "banana"
      get_item("a|b|c", 10, "|") 返回 ""
--]]
function get_item(_list, number, separate)
    -- 参数类型检查
    if type(_list) ~= "string" or type(number) ~= "number" then
        return ""
    else
        -- 设置默认分隔符
        separate = separate or "|"
        local fired = ""
        local count = 0
        -- 使用gmatch遍历所有项目
        for k in string.gmatch(_list .. separate, "(.-)" .. separate) do
            count = count + 1
            -- 找到指定位置的项目
            if count == number then
                fired = k
            end
        end
        return fired
    end
end

--[[
函数名: num_item
功能: 计算分隔符分隔的列表中项目的数量
参数:
  _item (string) - 列表字符串，如"a|b|c|d|e"
  separate (string, 可选) - 分隔符，默认为"|"
返回值: number - 列表中项目的数量，无效输入返回0
示例: num_item("a|b|c|d|e", "|") 返回 5
      num_item("apple,banana,orange", ",") 返回 3
      num_item("single", "|") 返回 1
      num_item("", "|") 返回 0
--]]
function num_item(_item, separate)
    -- 检查参数是否为有效字符串
    if _item and type(_item) == "string" then
        -- 设置默认分隔符
        separate = separate or "|"
        -- 如果字符串中包含分隔符，则计算项目数量
        if string.find(_item, separate) then
            local count = 0
            -- 使用gmatch遍历所有项目并计数
            for k in string.gmatch(_item .. separate, "(.-)" .. separate) do
                count = count + 1
            end
            return count
        else
            -- 如果没有分隔符，说明只有一个项目
            return 1
        end
    else
        -- 无效输入返回0
        return 0
    end
end

--[[
函数名: delete
功能: 从字符串中删除所有指定的子字符串
参数:
  str (string|number) - 原始字符串
  substr (string|number) - 要删除的子字符串
返回值: string - 删除指定子字符串后的结果，参数为nil时返回空字符串
示例: delete("hello world", "world") 返回 "hello "
      delete("abcabc", "a") 返回 "bcbc"
      delete(123, 2) 返回 "13"
--]]
function delete(str, substr)
    -- 参数为nil的处理
    if str == nil or substr == nil then
        return ""
    else
        -- 将数字类型转换为字符串
        if type(str) == "number" then
            str = tostring(str)
        end
        if type(substr) == "number" then
            substr = tostring(substr)
        end
        -- 使用string.gsub删除所有匹配的子字符串
        return string.gsub(str, substr, "")
    end
end

--[[
函数名: change
功能: 将字符串中所有指定的子字符串替换为目标字符串
参数:
  str (string|number) - 原始字符串
  substr (string|number) - 要替换的子字符串
  target (string|number) - 替换后的目标字符串
返回值: string - 替换后的结果字符串
示例: change("hello world", "world", "lua") 返回 "hello lua"
      change("abcabc", "a", "x") 返回 "xbcxbc"
      change(123, 2, 9) 返回 "193"
--]]
function change(str, substr, target)
    -- 参数为nil的处理
    if str == nil or substr == nil then
        return str
    else
        -- 将数字类型转换为字符串
        if type(str) == "number" then
            str = tostring(str)
        end
        if type(substr) == "number" then
            substr = tostring(substr)
        end
        -- 使用string.gsub进行替换
        return string.gsub(str, substr, target)
    end
end

--[[
函数名: trim
功能: 去除字符串两端的空白字符（空格、制表符、换行符等）
参数: s (string) - 要处理的字符串
返回值: string - 去除两端空白后的字符串，参数为nil时返回空字符串
示例: trim("  hello world  ") 返回 "hello world"
      trim("\t\n  test  \n\t") 返回 "test"
      trim("") 返回 ""
正则说明: "^%s*(.-)%s*$" 匹配开头的空白、中间内容、结尾的空白
--]]
function trim(s)
    if s ~= nil then
        -- 使用正则表达式去除首尾空白字符
        -- ^%s* 匹配开头的空白字符
        -- (.-) 捕获中间的内容（非贪婪匹配）
        -- %s*$ 匹配结尾的空白字符
        return (string.gsub(s, "^%s*(.-)%s*$", "%1"))
    else
        return ""
    end
end

--[[
函数名: begins
功能: 检查字符串是否以指定的子字符串开头
参数:
  str (string) - 要检查的字符串
  substr (string) - 开头子字符串
返回值: number - 以指定字符串开头返回1，否则返回0
示例: begins("hello world", "hello") 返回 1
      begins("hello world", "world") 返回 0
      begins("", "test") 返回 0
--]]
function begins(str, substr)
    -- 参数为nil的处理
    if str == nil or substr == nil then
        return 0
    end
    -- 使用string.find检查是否从位置1开始匹配
    if string.find(str, substr) ~= 1 then
        return 0
    else
        return 1
    end
end

--[[
函数名: ends
功能: 检查字符串是否以指定的子字符串结尾
参数:
  str (string) - 要检查的字符串
  substr (string) - 结尾子字符串
返回值: number - 以指定字符串结尾返回1，否则返回0
示例: ends("hello world", "world") 返回 1
      ends("hello world", "hello") 返回 0
算法说明: 通过反转字符串，将"结尾匹配"转换为"开头匹配"来实现
--]]
function ends(str, substr)
    -- 参数为nil的处理
    if str == nil or substr == nil then
        return 0
    end
    -- 反转原字符串和子字符串
    str_tmp = string.reverse(str)
    substr_tmp = string.reverse(substr)
    -- 检查反转后的字符串是否以反转后的子字符串开头
    if string.find(str_tmp, substr_tmp) ~= 1 then
        return 0
    else
        return 1
    end
end

--[[
函数名: right
功能: 获取字符串从指定位置之后的所有字符
参数:
  str (string|number) - 原始字符串
  pos (number) - 位置索引（从1开始）
返回值: string - 从pos+1位置开始到字符串末尾的子字符串
示例: right("hello world", 5) 返回 " world"
      right("abcdef", 2) 返回 "cdef"
      right("test", 10) 返回 ""
--]]
function right(str, pos)
    -- 参数类型检查
    if type(pos) == "number" and (type(str) == "string" or type(str) == "number")
    then
        -- 从pos+1位置开始截取到字符串末尾
        local str1 = string.sub(str, pos + 1)
        return str1
    else
        -- 参数无效时返回原字符串
        return str
    end
end

--[[
函数名: rightback
功能: 获取字符串从指定位置开始的所有字符（包含该位置）
参数:
  str (string|number) - 原始字符串
  pos (number) - 位置索引（从1开始）
返回值: string - 从pos位置开始到字符串末尾的子字符串
示例: rightback("hello world", 5) 返回 "o world"
      rightback("abcdef", 2) 返回 "bcdef"
      rightback("test", 1) 返回 "test"
--]]
function rightback(str, pos)
    -- 参数类型检查
    if type(pos) == "number" and (type(str) == "string" or type(str) == "number")
    then
        -- 从pos位置开始截取到字符串末尾
        local str1 = string.sub(str, pos)
        return str1
    else
        -- 参数无效时返回原字符串
        return str
    end
end

--[[
函数名: left
功能: 获取字符串从开头到指定位置的所有字符
参数:
  str (string|number) - 原始字符串
  pos (number) - 位置索引（从1开始）
返回值: string - 从字符串开头到pos位置的子字符串
示例: left("hello world", 5) 返回 "hello"
      left("abcdef", 3) 返回 "abc"
      left("test", 10) 返回 "test"
--]]
function left(str, pos)
    -- 参数类型检查
    if type(pos) == "number" and (type(str) == "string" or type(str) == "number")
    then
        -- 从字符串开头截取到pos位置
        local str1 = string.sub(str, 1, pos)
        return str1
    else
        -- 参数无效时返回原字符串
        return str
    end
end

--[[
函数名: leftback
功能: 获取字符串从开头到倒数第pos+1个字符的所有字符
参数:
  str (string|number) - 原始字符串
  pos (number) - 从末尾开始的位置索引
返回值: string - 从字符串开头到倒数第pos+1个字符的子字符串
示例: leftback("hello world", 5) 返回 "hello "
      leftback("abcdef", 2) 返回 "abc"
算法说明: 使用负数索引 -(pos + 1) 来实现从末尾计算位置
--]]
function leftback(str, pos)
    -- 参数类型检查
    if type(pos) == "number" and (type(str) == "string" or type(str) == "number")
    then
        -- 使用负数索引从末尾计算位置
        local str1 = string.sub(str, 1, -(pos + 1))
        return str1
    else
        -- 参数无效时返回原字符串
        return str
    end
end

--[[
函数名: pos
功能: 查找子字符串在原字符串中第一次出现的位置
参数:
  posi (string|number) - 要查找的子字符串
  str (string|number) - 原字符串
返回值: number - 子字符串第一次出现的位置（从1开始），未找到返回0
示例: pos("world", "hello world") 返回 7
      pos("test", "hello world") 返回 0
      pos("l", "hello") 返回 3
--]]
function pos(posi, str)
    -- 参数类型检查
    if (type(posi) == "number" or type(posi) == "string") and (type(str) == "string" or type(str) == "number") then
        -- 使用string.find查找子字符串位置
        local l, k = string.find(str, posi)
        if type(l) == "nil" then
            -- 未找到返回0
            return 0
        elseif l > 0 then
            -- 找到返回位置
            return l
        else
            return 0
        end
    else
        -- 参数无效返回0
        return 0
    end
end

--[[
函数名: copy
功能: 从字符串的指定位置开始复制指定长度的子字符串
参数:
  str (string|number) - 原始字符串
  start (number) - 开始位置（从1开始）
  lens (number) - 要复制的字符长度
返回值: string - 复制的子字符串
示例: copy("hello world", 7, 5) 返回 "world"
      copy("abcdef", 2, 3) 返回 "bcd"
      copy("test", 1, 2) 返回 "te"
--]]
function copy(str, start, lens)
    -- 参数类型检查
    if type(start) == "number" and type(lens) == "number" and (type(str) == "string" or type(str) == "number") then
        -- 从start位置开始，复制lens长度的字符
        local str1 = string.sub(str, start, (start + lens - 1))
        return str1
    else
        -- 参数无效时返回原字符串
        return str
    end
end

--[[
函数名: ismember
功能: 检查指定值是否存在于表中，并返回其位置
参数:
  str (string|number) - 要查找的值
  tab (table) - 要搜索的表
返回值: number - 如果找到返回在表中的位置（从1开始），未找到返回0
示例: ismember("apple", {"banana", "apple", "orange"}) 返回 2
      ismember(3, {1, 2, 3, 4}) 返回 3
      ismember("test", {"a", "b", "c"}) 返回 0
--]]
function ismember(str, tab)
    -- 参数类型检查：str必须是字符串或数字
    if type(str) ~= "string" and type(str) ~= "number" then
        return 0
        -- 参数类型检查：tab必须是表
    elseif type(tab) ~= "table" then
        return 0
    else
        local member = 0
        local count = 0
        -- 遍历表中的所有值
        for _, v in pairs(tab) do
            count = count + 1
            -- 找到匹配的值
            if str == v then
                member = count
                break
            end
        end
        return member
    end
end

--[[
函数名: is_table_empty
功能: 检查表是否为空
参数: t (table) - 要检查的表
返回值: boolean - 表为空返回true，否则返回false
示例: is_table_empty({}) 返回 true
      is_table_empty({1, 2, 3}) 返回 false
      is_table_empty({a = 1}) 返回 false
算法说明: 使用_G.next()函数检查表是否有下一个元素
--]]
function is_table_empty(t)
    -- 使用next函数检查表是否为空
    return _G.next(t) == nil
end

--[[
函数名: copy_table
功能: 浅拷贝表（复制表的第一层内容）
参数: object (any) - 要复制的对象，如果不是表则直接返回
返回值: table|any - 复制后的新表，保持原表的元表
示例: copy_table({a = 1, b = 2}) 返回新表 {a = 1, b = 2}
      copy_table("string") 返回 "string"
注意: 这是浅拷贝，嵌套的表仍然是引用关系
--]]
function copy_table(object)
    -- 如果不是表类型，直接返回原对象
    if type(object) ~= "table" then
        return object
    else
        local new_table = {}
        -- 复制表中的所有键值对
        for index, value in pairs(object) do
            new_table[index] = value
        end
        -- 保持原表的元表
        return setmetatable(new_table, getmetatable(object))
    end
end

--[[
函数名: list_to_table
功能: 将分隔符分隔的字符串转换为表
参数: list (string) - 分隔符分隔的字符串，默认使用"|"作为分隔符
返回值: table - 包含所有项目的数组表
示例: list_to_table("a|b|c|d") 返回 {"a", "b", "c", "d"}
      list_to_table("apple|banana|orange") 返回 {"apple", "banana", "orange"}
      list_to_table("") 返回 {}
用途: 主要用于将变量字符串转换为表格式便于处理
--]]
function list_to_table(list)
    local t = {}
    -- 设置默认值为空字符串
    list = list or ""
    -- 使用gmatch遍历所有由"|"分隔的项目
    for k, _ in string.gmatch(list .. '|', '(.-)|') do
        -- 只添加非空项目到表中
        if k ~= "" then
            table.insert(t, k)
        end
    end
    return t
end

--[[
函数名: setvar
功能: 设置全局变量并打印设置信息
参数:
  l (string) - 变量名
  w (string|number) - 变量值
返回值: 无
功能说明:
- 如果值可以转换为数字，则存储为数字类型
- 否则存储为字符串类型
- 设置成功后会打印变量名和值
示例: setvar("name", "test") 设置 var["name"] = "test" 并打印
      setvar("count", "123") 设置 var["count"] = 123 并打印
前提: 需要全局变量var表存在
--]]
function setvar(l, w)
    -- 检查参数是否有效
    if l ~= nil and w ~= nil and l ~= "" and w ~= "" then
        -- 尝试将值转换为数字
        if tonumber(w) == nil then
            -- 无法转换为数字，存储为字符串
            var[l] = w
            print("  " .. l .. " = " .. w)
        else
            -- 可以转换为数字，存储为数字类型
            var[l] = tonumber(w)
            print("  " .. l .. " = " .. w)
        end
    end
end

------------------------------------------------------------------
-------------------------------------------------------------------

-- 查询物品携带数量的函数，类似TinTin++的carryqty
-- 参数说明：
-- item_id: 物品ID，如"da huandan"
-- 返回值：物品数量，如果没有则返回0
function carryqty(item_id)
    -- 如果参数为空，返回0
    if not item_id or item_id == "" then
        return 0
    end

    -- 如果物品表中没有该物品，返回0
    if not item or not item[item_id] then
        return 0
    end

    -- 返回物品数量
    return item[item_id]
end

------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------

-- 通用触发器管理函数
-- trigger_prefix: 触发器名称前缀
-- start_index: 开始索引
-- end_index: 结束索引
function manage_triggers(trigger_prefix, start_index, end_index, action)
    for i = start_index, end_index do
        local trigger_id = trigger_prefix .. "_" .. i
        local action_text
        if action == "close" then
            close_trigger(trigger_id)
            action_text = "已关闭触发器"
        elseif action == "open" then
            open_trigger(trigger_id)
            action_text = "已打开触发器"
        elseif action == "delete" then
            del_trigger(trigger_id)
            action_text = "已删除触发器"
        end

        if action_text and var["debug"] and var["debug"] > 5 then
            echo(string.format("%s[调试]%s %s %s", C.g, C.x, action_text, C.c .. trigger_id))
        end
    end
end

-- 关闭指定前缀和范围的触发器
function close_triggers(trigger_prefix, start_index, end_index)
    manage_triggers(trigger_prefix, start_index, end_index, "close")
end

-- 打开指定前缀和范围的触发器
function open_triggers(trigger_prefix, start_index, end_index)
    manage_triggers(trigger_prefix, start_index, end_index, "open")
end

-- 删除指定前缀和范围的触发器
function delete_triggers(trigger_prefix, start_index, end_index)
    manage_triggers(trigger_prefix, start_index, end_index, "delete")
end

-- 管理零散索引的触发器
-- trigger_prefix: 触发器名称前缀
-- indices: 索引表，如 {1, 3, 6, 11, 12}
-- action: 操作类型，"close", "open" 或 "delete"
function manage_scattered_triggers(trigger_prefix, indices, action)
    for _, i in ipairs(indices) do
        local trigger_id = trigger_prefix .. "_" .. i
        local action_text
        if action == "close" then
            close_trigger(trigger_id)
            action_text = "已关闭触发器"
        elseif action == "open" then
            open_trigger(trigger_id)
            action_text = "已打开触发器"
        elseif action == "delete" then
            del_trigger(trigger_id)
            action_text = "已删除触发器"
        end

        if action_text and var["debug"] and var["debug"] > 5 then
            echo(string.format("%s[调试]%s %s %s", C.g, C.x, action_text, C.c .. trigger_id))
        end
    end
end

-- 关闭指定前缀和零散索引的触发器
function close_scattered_triggers(trigger_prefix, indices)
    manage_scattered_triggers(trigger_prefix, indices, "close")
end

-- 打开指定前缀和零散索引的触发器
function open_scattered_triggers(trigger_prefix, indices)
    manage_scattered_triggers(trigger_prefix, indices, "open")
end

-- 删除指定前缀和零散索引的触发器
function delete_scattered_triggers(trigger_prefix, indices)
    manage_scattered_triggers(trigger_prefix, indices, "delete")
end

--用法示例：
--[[
-- 关闭otherquest_task_1到otherquest_task_5的触发器
close_triggers("otherquest_task", 1, 5)

-- 打开fight_1到fight_10的触发器
open_triggers("fight", 1, 10)

-- 删除quest_1到quest_3的触发器
delete_triggers("quest", 1, 3)

-- 关闭fight_1, fight_3, fight_6, fight_11, fight_12的触发器
close_scattered_triggers("fight", {1, 3, 6, 11, 12})

-- 打开otherquest_task_2, otherquest_task_5, otherquest_task_8的触发器
open_scattered_triggers("otherquest_task", {2, 5, 8})

-- 删除quest_1, quest_4, quest_7的触发器
delete_scattered_triggers("quest", {1, 4, 7})
--]]

------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------

-- 通用A*寻路算法
-- start_room: 起始房间ID
-- end_room: 目标房间ID
-- options: 可选参数表，包含以下可选项：
--   - room_list: 可用房间列表，默认为nil(使用所有房间)
--   - heuristic_func: 启发式函数，默认为曼哈顿距离
--   - get_neighbors_func: 获取相邻房间函数，默认使用exits表
--   - cost_func: 计算移动成本函数，默认为1
--   - max_iterations: 最大迭代次数，默认为10000
--   - debug: 是否输出调试信息，默认为false
-- 添加通用A*寻路算法
function astar_search(start_room, end_room, options)
    options = options or {}
    local room_list = options.room_list
    local max_iterations = options.max_iterations or 10000
    local debug = options.debug or false
    local max_radius = options.max_radius or 5 -- 添加最大搜索半径参数，默认100

    -- 默认启发式函数：曼哈顿距离
    local function default_heuristic(room_id, goal_id)
        if not rooms[room_id] or not rooms[goal_id] then
            return 999999 -- 如果房间不存在，返回一个很大的值
        end

        local x1, y1 = rooms[room_id].x or 0, rooms[room_id].y or 0
        local x2, y2 = rooms[goal_id].x or 0, rooms[goal_id].y or 0

        local distance = math.abs(x1 - x2) + math.abs(y1 - y2)

        -- 如果距离超过最大半径，返回一个很大的值
        if distance > max_radius then
            return 999999
        end

        return distance
    end

    -- 默认获取相邻房间函数
    local function default_get_neighbors(room_id)
        local neighbors = {}
        if not rooms[room_id] or not rooms[room_id].exits then
            return neighbors
        end

        for dir, next_room in pairs(rooms[room_id].exits) do
            -- 如果提供了房间列表，则只考虑列表中的房间
            if not room_list or table_contains(room_list, next_room) then
                table.insert(neighbors, { room = next_room, dir = dir, cost = 1 })
            end
        end

        return neighbors
    end
   

    -- 使用自定义或默认函数
    local heuristic = options.heuristic_func or default_heuristic
    local get_neighbors = options.get_neighbors_func or default_get_neighbors

    -- 优先队列实现
    local open_set = {}
    local closed_set = {}
    local g_score = {}    -- 从起点到当前点的实际代价
    local f_score = {}    -- 估计总代价
    local came_from = {}  -- 记录路径
    local directions = {} -- 记录方向

    -- 初始化起点
    g_score[start_room] = 0
    f_score[start_room] = heuristic(start_room, end_room)
    table.insert(open_set, start_room)

    local iterations = 0

    while #open_set > 0 and iterations < max_iterations do
        iterations = iterations + 1

        -- 找到open_set中f_score最小的节点
        local current_index = 1
        for i = 2, #open_set do
            if f_score[open_set[i]] < f_score[open_set[current_index]] then
                current_index = i
            end
        end

        local current = open_set[current_index]

        -- 如果到达目标，构建路径并返回
        if current == end_room then
            local path = { current }
            local path_dirs = {}

            while came_from[current] do
                current = came_from[current]
                table.insert(path, 1, current)
                if directions[current] then
                    table.insert(path_dirs, 1, directions[current])
                end
            end

            if debug then
                echo("\nA*搜索完成，迭代次数: " .. iterations)
                echo("\n路径: " .. table.concat(path, " -> "))
                echo("\n方向: " .. table.concat(path_dirs, ", "))
            end

            return path, path_dirs
        end

        -- 从open_set中移除当前节点
        table.remove(open_set, current_index)
        -- 添加到closed_set
        table.insert(closed_set, current)

        -- 检查所有邻居
        for _, neighbor_info in ipairs(get_neighbors(current)) do
            local neighbor = neighbor_info.room
            local dir = neighbor_info.dir
            local move_cost = neighbor_info.cost or 1

            -- 如果邻居已经在closed_set中，跳过
            if not table_contains(closed_set, neighbor) then
                -- 计算从起点经过当前节点到邻居的代价
                local tentative_g_score = (g_score[current] or 0) + move_cost

                -- 如果邻居不在open_set中，或者找到了更好的路径
                if not table_contains(open_set, neighbor) or tentative_g_score < (g_score[neighbor] or math.huge) then
                    -- 更新路径信息
                    came_from[neighbor] = current
                    directions[neighbor] = dir
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = tentative_g_score + heuristic(neighbor, end_room)

                    -- 如果邻居不在open_set中，添加它
                    if not table_contains(open_set, neighbor) then
                        table.insert(open_set, neighbor)
                    end
                end
            end
        end
    end

    if debug then
        echo("\nA*搜索失败，迭代次数: " .. iterations)
    end

    -- 没有找到路径
    return nil, nil
end

-- 通用任务搜索函数
-- 参数：target_area(目标区域), search_range(搜索范围), npc_name(NPC名字), max_attempts(最大尝试次数), custom_action(自定义搜索语句), log_prefix(日志前缀), special_handler(特殊处理函数), fangqi_options(放弃选项)
function universal_job_search(target_area, search_range, npc_name, max_attempts, custom_action, log_prefix,
                              special_handler, fangqi_options)
    max_attempts = max_attempts or 2
    search_range = search_range or 0
    custom_action = custom_action or "alias action A*搜索中..."
    log_prefix = log_prefix or "A * 搜索"
    fangqi_options = fangqi_options or {}

    local current_attempt = 1

    local function start_search()
        if current_attempt > max_attempts then
            local os_date = os.date("%m/%d %H:%M:%S")
            echo("\n" .. C.c .. "<Lua>:" .. os_date .. C.r .. "【" .. log_prefix .. "】:搜索失败，已达到最大尝试次数。")
            exec("job_fail")
            return
        end

        local os_date = os.date("%m/%d %H:%M:%S")
        echo("\n" ..
        C.c ..
        "<Lua>:" ..
        os_date .. C.x .. "【" .. log_prefix .. "】:第" ..
        current_attempt .. "次搜索，目标区域【" .. target_area .. "】，寻找【" .. npc_name .. "】。")

        -- 获取搜索列表
        local actual_range = search_range == 0 and 2 or (search_range + 2)
        local room_list, search_list, search_list2, job_zone, job_room = get_job_rooms(
            target_area, actual_range, npc_name,
            fangqi_options.killer_party, fangqi_options.killer_skill,
            fangqi_options.black_list, fangqi_options.fangqi_party,
            fangqi_options.fangqi_skill, fangqi_options.fangqi_zone_room,
            fangqi_options.fangqi_zone, fangqi_options.sort or 1
        )

        if null(room_list) then
            local os_date = os.date("%m/%d %H:%M:%S")
            echo("\n" .. C.c .. "<Lua>:" .. os_date .. C.r .. "【" .. log_prefix .. "】:搜索地点【" .. target_area .. "】失败。")
            exec("job_fail")
            return
        end

        -- 保存搜索数据
        var["room_list"] = room_list
        var["search_list"] = current_attempt == 1 and search_list or search_list2
        var["job_zone"] = job_zone
        var["job_room"] = job_room
        var["job_step"] = 0

        -- 特殊处理
        if special_handler then
            special_handler()
        end

        -- 设置搜索alias（只设置一次）
        add_alias("cross_for_job", function(params)
            if var["do_stop"] == 0 then
                local job_step = var["job_step"] or "none"
                if job_step ~= "done" then
                    exes(custom_action, 2)
                end
            end
        end)

        add_alias("cross_for_end", function(params)
            var["job_step"] = 0
            send("time")
            current_attempt = current_attempt + 1
            if current_attempt <= max_attempts then
                local os_date = os.date("%m/%d %H:%M:%S")
                echo("\n" ..
                C.c ..
                "<Lua>:" ..
                os_date .. C.y .. "【" .. log_prefix .. "】:第" .. (current_attempt - 1) ..
                "次搜索失败，开始第" .. current_attempt .. "次搜索。")
                start_search()
            else
                local os_date = os.date("%m/%d %H:%M:%S")
                echo("\n" .. C.c .. "<Lua>:" .. os_date .. C.r .. "【" .. log_prefix .. "】:所有搜索尝试失败，放弃任务。")
                exec("job_fail")
            end
        end)

        -- 开始搜索
        function after_gps()
            local search_path, port = get_searches_path(var["search_list"], "cross_for_job", lua_flags)
            var["port"] = port
            var["search_path"] = search_path

            function after_goto()
                var["path_after"] = var["search_path"] .. ";cross_for_job;cross_for_end"
                keepwalk()
            end

            exec("goto @port")
        end

        exec("gps")
    end

    start_search()
end

-------------------------------------------------------------------------------------------------
-------------------------------------------------------------------------------------------------

-- 添加通用日志输出函数
-- 参数说明：
-- message: 日志消息内容
-- color: 颜色代码，默认为C.x
-- prefix: 日志前缀，默认为"<解密>"
-- how_player: 是否显示玩家ID，1为显示，0为不显示，默认为1
function log_message(message, color, prefix, how_player)
    local id = var["char_id"] or "none"
    local os_date = os.date("%m/%d %H:%M:%S")
    color = color or C.x
    prefix = prefix or "<解密>"
    how_player = how_player == nil and 1 or how_player -- 如果未传入how_player，默认为1

    local output = "\n" .. C.c .. prefix .. ":" .. os_date
    if how_player == 1 then
        output = output .. color .. "【玩家:" .. id .. "】："
    else
        output = output .. color
    end
    output = output .. message

    echo(output)
end

--[[
-- 用法示例：
-- 1. 基本用法（显示玩家ID）
log_message("这是一条日志消息", C.y, "<解密>")

-- 2. 不显示玩家ID
log_message("这是一条日志消息", C.y, "<解密>", 0)

-- 3. 显式指定显示玩家ID
log_message("这是一条日志消息", C.y, "<解密>", 1)
--]]

-------------------------------------------------------------------------------------------------
-------------------------------------------------------------------------------------------------

-- 购买大还丹的通用函数
-- 参数说明：
-- target_count: 目标数量，希望携带的大还丹总数
-- callback: 购买完成后执行的回调函数，可选
function buy_da_huandan(target_count, callback)
    -- 使用i命令检查物品
    exec("i")
    wait(0.5, function()
        -- 检查当前携带的大还丹数量
        local current_count = carryqty("da huandan")

        -- 计算需要购买的数量
        local need_count = target_count - current_count

        -- 如果已经有足够的大还丹，不需要购买
        if need_count <= 0 then
            --log_message("已有足够的大还丹，当前数量: " .. current_count, C.g)
            -- 如果有回调函数，直接执行
            if callback then
                callback()
            end
            return
        end

        -- 输出购买信息
        --log_message("当前大还丹数量: " .. current_count .. "，目标数量: " .. target_count .. "，需要购买: " .. need_count, C.y)

        -- 前往扬州当铺(92)
        g(92, function()
            -- 执行购买逻辑
            buy_huandan_recursive(need_count, target_count, callback)
        end)
    end)

    return true
end

--[[
-- 用法示例：
-- 确保携带10个大还丹，不足则前往扬州当铺购买，完成后执行回调函数
buy_da_huandan(10, function()
    log_message("购买完成后执行的操作", C.g)
    -- 这里可以添加其他命令
    -- exec("其他命令")
end)
--]]

-- 递归购买大还丹的辅助函数
-- 由于每次只能买一个，需要递归调用
function buy_huandan_recursive(remaining, target_count, callback)
    -- 如果已经购买完所需数量，结束递归
    if remaining <= 0 then
        -- 使用i命令检查物品
        exec("i")
        wait(0.5, function()
            local final_count = carryqty("da huandan")
            --log_message("大还丹购买完成，当前数量: " .. final_count, C.g)

            -- 如果有回调函数，执行它
            if callback then
                callback()
            end
        end)
        return
    end

    -- 使用check_busy2检查是否繁忙
    check_busy2(function()
        -- 执行购买命令（每次只能买一个）
        exec("duihuan da huandan")

        -- 延迟一段时间后检查是否购买成功并继续递归
        wait(1, function()
            -- 使用i命令检查物品
            exec("i")
            wait(0.5, function()
                local current_count = carryqty("da huandan")
                local previous_count = target_count - remaining

                -- 检查是否购买成功（当前数量比之前多）
                if current_count > previous_count then
                    -- 购买成功，继续购买下一个
                    buy_huandan_recursive(target_count - current_count, target_count, callback)
                else
                    -- 购买失败，但继续尝试购买
                    --log_message("购买未成功，继续尝试...", C.y)
                    -- 继续尝试购买，不减少remaining
                    buy_huandan_recursive(remaining, target_count, callback)
                end
            end)
        end)
    end)
end

-----------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------

-- 通用物品购买函数
-- 参数说明：
-- item_id: 物品ID，如"da huandan"
-- target_count: 目标数量，希望携带的物品总数
-- shop_room_id: 购买物品的商店房间ID
-- buy_command: 购买物品的命令，如"duihuan da huandan"
-- callback: 购买完成后执行的回调函数，可选
-- options: 可选参数表，包含以下可选项：
--   - delay: 购买间隔延迟，默认为1秒
--   - check_delay: 检查物品延迟，默认为0.5秒
--   - prefix: 日志前缀，默认为"<解密>"
function buy_item(item_id, target_count, shop_room_id, buy_command, callback, options)
    options = options or {}
    local delay = options.delay or 1
    local check_delay = options.check_delay or 0.5
    local prefix = options.prefix or "<解密>"

    -- 使用i命令检查物品
    exec("i")
    wait(check_delay, function()
        -- 检查当前携带的物品数量
        local current_count = carryqty(item_id)

        -- 计算需要购买的数量
        local need_count = target_count - current_count

        -- 如果已经有足够的物品，不需要购买
        if need_count <= 0 then
            log_message("已有足够的" .. item_id .. "，当前数量: " .. current_count, C.g, prefix)
            -- 如果有回调函数，直接执行
            if callback then
                callback()
            end
            return true
        end

        -- 输出购买信息
        log_message("当前" .. item_id .. "数量: " .. current_count .. "，目标数量: " .. target_count .. "，需要购买: " .. need_count,
            C.y, prefix)

        -- 前往指定商店
        g(shop_room_id, function()
            -- 执行购买逻辑
            buy_item_recursive(item_id, need_count, target_count, buy_command, callback, delay, check_delay, prefix)
        end)
    end)

    return true
end

-- 递归购买物品的辅助函数
function buy_item_recursive(item_id, remaining, target_count, buy_command, callback, delay, check_delay, prefix)
    -- 如果已经购买完所需数量，结束递归
    if remaining <= 0 then
        -- 使用i命令检查物品
        exec("i")
        wait(check_delay, function()
            local final_count = carryqty(item_id)
            log_message(item_id .. "购买完成，当前数量: " .. final_count, C.g, prefix)

            -- 如果有回调函数，执行它
            if callback then
                callback()
            end
        end)
        return
    end

    -- 使用check_busy2检查是否繁忙
    check_busy2(function()
        -- 执行购买命令
        exec(buy_command)

        -- 延迟一段时间后检查是否购买成功并继续递归
        wait(delay, function()
            -- 使用i命令检查物品
            exec("i")
            wait(check_delay, function()
                local current_count = carryqty(item_id)
                local previous_count = target_count - remaining

                -- 检查是否购买成功（当前数量比之前多）
                if current_count > previous_count then
                    -- 购买成功，继续购买下一个
                    buy_item_recursive(item_id, target_count - current_count, target_count, buy_command, callback, delay,
                        check_delay, prefix)
                else
                    -- 购买失败，但继续尝试购买
                    log_message("购买未成功，继续尝试...", C.y, prefix)
                    -- 继续尝试购买，不减少remaining
                    buy_item_recursive(item_id, remaining, target_count, buy_command, callback, delay, check_delay,
                        prefix)
                end
            end)
        end)
    end)
end

--[[
-- 用法示例：
-- 确保携带10个大还丹，不足则前往扬州当铺购买，完成后执行回调函数
buy_item("da huandan", 10, 92, "duihuan da huandan", function()
    log_message("购买完成后执行的操作", C.g)
    -- 这里可以添加其他命令
    -- exec("其他命令")
end)

-- 确保携带5个金创药，不足则前往药店购买
buy_item("jin chuang yao", 5, 93, "buy jin chuang yao", nil, {prefix = "<药品购买>"})
--]]

-----------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------

-- 通用HP检测函数
-- 参数说明：
-- trigger_name: 触发器名称，如"otherquest_tongtianta_1"
-- options: 可选参数表，包含以下可选项：
--   - qi_threshold: 气血百分比阈值，低于此值时执行恢复命令，默认为80
--   - neili_threshold: 内力阈值，低于此值时执行恢复命令，默认为5000
--   - recovery_command: 恢复命令，默认为"fu dan"
--   - callback: 自定义回调函数，可以根据HP信息执行自定义操作
function setup_hp_monitor(trigger_name, options)
    options = options or {}
    local qi_threshold = options.qi_threshold or 80
    local neili_threshold = options.neili_threshold or 5000
    local recovery_command = options.recovery_command or "fu dan"
    local callback = options.callback

    -- 关闭已存在的同名触发器（如果有）
    close_trigger(trigger_name)

    -- 添加新的HP监控触发器
    add_trigger(trigger_name, "^\\s*HP:(.+)", function(params)
        local jing, maxjing, hurtjing, jingli, maxjingli, qi, maxqi, hurtqi, neili, maxneili, jiali, shen, food, pot, maxpot, water, myexp =
        string.match(params[1],
            "(.%d*)/(%d-)/(%d-)%%/(.%d*)/(%d-)/%d-/(.%d*)/(%d-)/(%d-)%%/(.%d*)/(%d-)/%+(%d-)/(.%d-)/%d-/%d-/(%d-)%.%d%d%%/(%d-)/(%d-)/(%d-)%.%d%d%%/(%d-)/%d")

        -- 转换为数字并存入变量
        var["jing"], var["maxjing"], var["hurtjing"] = tonumber(jing), tonumber(maxjing), tonumber(hurtjing)
        var["jingli"], var["maxjingli"] = tonumber(jingli), tonumber(maxjingli)
        var["qi"], var["maxqi"], var["hurtqi"] = tonumber(qi), tonumber(maxqi), tonumber(hurtqi)
        var["neili"], var["maxneili"], var["jiali"] = tonumber(neili), tonumber(maxneili), tonumber(jiali)
        var["shen"] = tonumber(shen)
        var["food"], var["pot"], var["maxpot"] = tonumber(food), tonumber(pot), tonumber(maxpot)
        var["water"], var["exp"] = tonumber(water), tonumber(myexp)

        -- 检查是否需要恢复
        if var["hurtqi"] < qi_threshold or var["neili"] < neili_threshold then
            exec(recovery_command)
        end

        -- 如果有自定义回调函数，执行它
        if callback then
            callback({
                jing = var["jing"],
                maxjing = var["maxjing"],
                hurtjing = var["hurtjing"],
                jingli = var["jingli"],
                maxjingli = var["maxjingli"],
                qi = var["qi"],
                maxqi = var["maxqi"],
                hurtqi = var["hurtqi"],
                neili = var["neili"],
                maxneili = var["maxneili"],
                jiali = var["jiali"],
                shen = var["shen"],
                food = var["food"],
                pot = var["pot"],
                maxpot = var["maxpot"],
                water = var["water"],
                exp = var["exp"]
            })
        end

        -- 清理临时变量
        jing, maxjing, hurtjing, jingli, maxjingli = nil, nil, nil, nil, nil
        qi, maxqi, hurtqi, neili, maxneili = nil, nil, nil, nil, nil
        jiali, shen, food, pot, maxpot = nil, nil, nil, nil, nil
        water, myexp = nil, nil
    end)

    return trigger_name
end

-- 移除HP监控触发器
function remove_hp_monitor(trigger_name)
    close_trigger(trigger_name)
end

--[[
-- 用法示例：
-- 基本用法（使用默认参数）
setup_hp_monitor("my_hp_monitor")

-- 自定义阈值和恢复命令
setup_hp_monitor("combat_hp_monitor", {
    qi_threshold = 70,
    neili_threshold = 3000,
    recovery_command = "yun recover;yun regenerate"
})

-- 使用自定义回调函数
setup_hp_monitor("advanced_hp_monitor", {
    callback = function(hp_data)
        -- 根据HP数据执行自定义逻辑
        if hp_data.hurtjing < 50 then
            exec("yun refresh")
        end

        if hp_data.food < 100 then
            exec("eat liang")
        end

        if hp_data.water < 100 then
            exec("drink jiudai")
        end
    end
})

-- 移除监控
remove_hp_monitor("my_hp_monitor")
--]]

--简易HP检测（保留原有功能，使用新函数实现）
--setup_hp_monitor("otherquest_tongtianta_1")

-----------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------
--通天塔层数和对应skills映射
-- 根据通天塔层数返回所在范围：1(1-798), 2(799-998), 3(999-1000)
function get_ttt_level_range(level)
    if not level then return 1 end

    level = tonumber(level)
    if level < 799 then
        return 1 -- 1-798层
    elseif level < 999 then
        return 2 -- 799-998层
    else
        return 3 -- 999-1000层
    end
end

-- 根据通天塔层数设置相应的技能和alias
function set_ttt_skills_by_level(level, start_level)
    if not level or not var["killer_id"] then return end

    level = tonumber(level)
    start_level = tonumber(start_level or 0)

    -- 判断是否是关键层（起始层、1层、799层或999层）
    local is_key_level = (level == start_level or level == 1 or level == 800 or level == 1000)

    -- 获取层数范围
    local range = get_ttt_level_range(level)

    -- 如果是超人模式，设置相应的技能
    if var["superman"] and (var["superman"] == 1 or var["superman"] == 2) then
        -- 检查是否需要显示技能设置消息（只显示一次）
        -- var["ttt_skills_message_shown"] 这个变量需要在每次通天塔开始时设置为 false
        if not var["ttt_skills_message_shown"] then
            if range == 1 then
                log_message("开始层数：" .. level .. "，超人模式，设置技能组合一", C.y, "<系统>")
                var["ttt_beiskills"] = var["ttt_beiskills_1"]
                var["ttt_pfm"] = var["ttt_pfm_1"]
            elseif range == 2 then
                log_message("开始层数：" .. level .. "，超人模式，设置技能组合二", C.y, "<系统>")
                var["ttt_beiskills"] = var["ttt_beiskills_2"]
                var["ttt_pfm"] = var["ttt_pfm_2"]
            elseif range == 3 then
                log_message("开始层数：" .. level .. "，超人模式，设置技能组合三", C.y, "<系统>")
                var["ttt_beiskills"] = var["ttt_beiskills_3"]
                var["ttt_pfm"] = var["ttt_pfm_3"]
            end
            -- 设置标志位为 true，表示消息已显示
            var["ttt_skills_message_shown"] = true
        else
            -- 如果消息已经显示过，只设置技能组合，不输出日志
            if range == 1 then
                var["ttt_beiskills"] = var["ttt_beiskills_1"]
                var["ttt_pfm"] = var["ttt_pfm_1"]
            elseif range == 2 then
                var["ttt_beiskills"] = var["ttt_beiskills_2"]
                var["ttt_pfm"] = var["ttt_pfm_2"]
            elseif range == 3 then
                var["ttt_beiskills"] = var["ttt_beiskills_3"]
                var["ttt_pfm"] = var["ttt_pfm_3"]
            end
        end

        -- 在关键层设置alias
        if is_key_level then
            send("alias pfm " .. expand(var["ttt_pfm"]))
            send("alias bei_skills " .. expand(var["ttt_beiskills"]))
            send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
        end
    end

    -- 根据不同模式和层数执行相应操作
    if var["superman"] and var["superman"] == 2 then -- 超2模式
        local ttt_wait_busy_1 = var["ttt_wait_busy_1"] or 2
        local ttt_wait_busy_2 = var["ttt_wait_busy_2"] or 2

        if is_key_level then
            wait(3, function()
                check_busy(function()
                    check_busy2(function()
                        exec("bei_skills")
                        exec("kill @pfm_id;pfm")
                    end)
                end)
            end)
        elseif level < 800 then
            wait(ttt_wait_busy_1, function()
                exec("kill @pfm_id;pfm")
            end)
        else
            wait(ttt_wait_busy_2, function()
                exec("kill @pfm_id;pfm")
            end)
        end
    elseif is_key_level then -- 关键层但非超2模式
        wait(3, function()
            check_busy(function()
                check_busy2(function()
                    exec("bei_skills")
                    exec("kill @pfm_id;pfm")
                end)
            end)
        end)
    else -- 非关键层
        check_busy2(function()
            -- 根据层数和AOE设置决定攻击方式
            if level < 799 and var["ttt_normal_aoe"] and var["ttt_normal_aoe"] == 1 then
                exec("kill guard 1;kill guard 2;kill guard 3;pfm")
            elseif level > 798 and level < 999 and var["ttt_super_aoe"] and var["ttt_super_aoe"] == 1 then
                exec("kill guard 1;kill guard 2;kill guard 3;pfm")
            elseif level > 998 and var["ttt_master_aoe"] and var["ttt_master_aoe"] == 1 then
                exec("kill guard 1;kill guard 2;kill guard 3;pfm")
            else
                exec("kill @pfm_id;pfm")
            end
        end)
    end
end

-----------------------------------------------------------
-----------------------------------------------------------

-- 通用计时函数
-- 全局变量用于存储时间
if not var.time_tracker then
    var.time_tracker = {}
end

-- 设置开始时间
-- 参数说明：
-- key: 时间标识符，用于区分不同的计时任务
function set_start_time(key)
    if not key then
        key = "default"
    end
    var.time_tracker[key] = {
        start_time = os.time()
    }
    return var.time_tracker[key].start_time
end

-- 设置结束时间
-- 参数说明：
-- key: 时间标识符，用于区分不同的计时任务
function set_end_time(key)
    if not key then
        key = "default"
    end
    if not var.time_tracker[key] then
        var.time_tracker[key] = {}
    end
    var.time_tracker[key].end_time = os.time()
    return var.time_tracker[key].end_time
end

-- 计算时间差
-- 参数说明：
-- key: 时间标识符，用于区分不同的计时任务
-- display_mode: 显示模式
--   0: 只显示秒
--   1: 显示分钟和秒
--   2: 显示小时、分钟和秒
-- 返回值：格式化后的时间字符串，如果时间未设置则返回nil

function calculate_time_diff(key, display_mode)
    if not key then
        key = "default"
    end

    -- 检查时间是否已设置
    if not var.time_tracker[key] or not var.time_tracker[key].start_time then
        return nil, "未设置开始时间"
    end

    -- 如果未设置结束时间，使用当前时间
    local end_time = var.time_tracker[key].end_time or os.time()
    local diff = end_time - var.time_tracker[key].start_time

    -- 如果时间差小于0，返回0
    if diff < 0 then
        diff = 0
    end

    -- 准备返回结果
    local result

    -- 根据显示模式返回不同格式
    if display_mode == 0 then
        -- 模式0：只显示秒
        result = align_right(diff) .. "秒"
    elseif display_mode == 1 then
        -- 模式1：显示分钟和秒
        local minutes = math.floor(diff / 60)
        local seconds = diff % 60
        result = string.format("%d分%02d秒", minutes, seconds)
    else
        -- 模式2：显示小时、分钟和秒
        local hours = math.floor(diff / 3600)
        local minutes = math.floor((diff % 3600) / 60)
        local seconds = diff % 60
        result = string.format("%02d小时%02d分%02d秒", hours, minutes, seconds)
    end

    -- 计算完成后自动清理这个key的记录
    var.time_tracker[key] = nil

    return result
end

--[[
-- 用法示例：

-- 开始一个默认计时
set_start_time()

-- 开始一个指定任务的计时
set_start_time("任务1")

-- 结束默认计时
set_end_time()

-- 结束指定任务的计时
set_end_time("任务1")

-- 计算并显示时间差（秒）
local time_diff = calculate_time_diff("任务1", 0)
log_message("任务耗时：" .. time_diff, C.y)

-- 计算并显示时间差（分秒）
local time_diff = calculate_time_diff("任务1", 1)
log_message("任务耗时：" .. time_diff, C.y)

-- 计算并显示时间差（时分秒）
local time_diff = calculate_time_diff("任务1", 2)
log_message("任务耗时：" .. time_diff, C.y)
--]]


---------------------
---------------------
-- 通用右对齐函数
-- 参数说明：
-- value: 要对齐的值（可以是数字或字符串）
-- width: 对齐的宽度（可选，默认为2）
-- 返回值：右对齐后的字符串
function align_right(value, width)
    width = width or 2 -- 如果未指定宽度，默认为2

    -- 判断输入类型并选择合适的格式化字符串
    local format_type = type(value)
    if format_type == "number" then
        return string.format("%" .. width .. "d", value)
    else
        -- 将输入转换为字符串并右对齐
        return string.format("%" .. width .. "s", tostring(value))
    end
end

--[[
-- 用法示例：
-- 1. 数字对齐
local num1 = align_right(5)      -- 返回" 5"（前面是一个空格）
local num2 = align_right(10)     -- 返回"10"

-- 2. 字符串对齐
local str1 = align_right("abc", 5)     -- 返回"  abc"
local str2 = align_right("测试", 6)    -- 返回"    测试"

-- 3. 混合使用
local name = "张三"
local score = 95
local output = align_right(name, 6) .. "：" .. align_right(score) .. "分"
-- 输出："    张三：95分"

-- 4. 在时间显示中的应用
local hours = 5
local minutes = 30
local seconds = 8
local time = align_right(hours) .. "时" .. align_right(minutes) .. "分" .. align_right(seconds) .. "秒"
-- 输出：" 5时30分 8秒"
--]]

----------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------
-- 通用内存统计函数
function show_memory_stats()
    -- 在统计前，强制进行一次完整的垃圾回收，以获得更准确的内存"净值"
    collectgarbage("collect")
    -- 1. 收集所有数据和统计函数数量
    local function count_functions_in_table(t)
        if not t or type(t) ~= "table" then return 0 end
        local count = 0
        for _, v in pairs(t) do
            if type(v) == "function" then
                count = count + 1
            end
        end
        return count
    end

    local var_names = {}
    if var then for k, _ in pairs(var) do table.insert(var_names, k) end end
    local var_count = #var_names
    local var_func_count = count_functions_in_table(var)

    local trigger_ids = {}
    if trig_rex then for k, _ in pairs(trig_rex) do table.insert(trigger_ids, k) end end
    local trigger_count = #trigger_ids
    -- 触发器表的值本身就是函数，所以数量是一样的
    local trigger_func_count = trigger_count

    local alias_names = {}
    if alias then for k, _ in pairs(alias) do table.insert(alias_names, k) end end
    local alias_count = #alias_names
    local alias_func_count = count_functions_in_table(alias)

    -- 假设 checkbusy.lua 中的 timers 是全局的
    local timer_func_count = count_functions_in_table(timers)

    -- 2. 计算内存
    local mem_kb = collectgarbage("count")
    local mem_str
    if mem_kb < 1024 then
        mem_str = string.format("%.2f KB", mem_kb)
    else
        mem_str = string.format("%.2f MB", mem_kb / 1024)
    end

    -- 3. 输出统计摘要 (总是显示)
    local summary = string.format(
        "变量: %d | 触发器: %d | 别名: %d (%d个函数) | Timers: %d个函数 | Lua总内存: %s",
        var_count,
        trigger_count,
        alias_count, alias_func_count,
        timer_func_count,
        mem_str
    )
    log_message("当前内存统计：" .. summary, C.G, "<解密>")

    -- 4. 如果调试等级大于5，则显示详细列表
    if var["debug"] and var["debug"] > 5 then
        -- 定义一个通用的列表格式化函数
        local function format_list_string(title, items, color)
            local count = #items
            if count == 0 then return "" end -- 如果列表为空，则不显示

            local result_string = "\n" .. color .. title
            local line_buffer = {}
            for i, item_name in ipairs(items) do
                table.insert(line_buffer, item_name)
                if i % 10 == 0 or i == count then
                    result_string = result_string .. "\n" .. color .. "  " .. table.concat(line_buffer, ", ")
                    line_buffer = {} -- 清空行缓冲
                end
            end
            return result_string
        end

        -- 生成并显示每个列表
        local var_list_str = format_list_string("  已加载变量列表:", var_names, C.Y)
        if var_list_str ~= "" then echo(var_list_str) end

        local trigger_list_str = format_list_string("  已加载触发器列表:", trigger_ids, C.G)
        if trigger_list_str ~= "" then echo(trigger_list_str) end

        local alias_list_str = format_list_string("  已加载别名列表:", alias_names, C.C)
        if alias_list_str ~= "" then echo(alias_list_str) end
    end
end

add_alias("memstat", function(params)
    show_memory_stats()
end)


--[[
函数名: convert_chinese_time
功能: 将中文时间格式转换为指定格式的总用时
参数:
  chinese_time (string) - 中文时间字符串，如"十九小时十八分三十六秒"
  format (string, 可选) - 返回格式，可选值：
    "seconds" 或 "s" - 返回总秒数
    "minutes" 或 "m" - 返回总分钟数（保留小数）
    "hours" 或 "h" - 返回总小时数（保留小数）
    "hm" - 返回"X小时Y分"格式
    "hms" - 返回"X小时Y分Z秒"格式（默认）
返回值: 根据format参数返回相应格式的时间
示例:
  convert_chinese_time("十九小时十八分三十六秒", "seconds") 返回 69516
  convert_chinese_time("十九小时十八分三十六秒", "hm") 返回 "19小时18分"
  convert_chinese_time("十九小时十八分三十六秒", "hms") 返回 "19小时18分36秒"
  convert_chinese_time("三分二十秒", "minutes") 返回 3.33
算法说明:
1. 使用正则表达式提取小时、分钟、秒数
2. 使用trans函数将中文数字转换为阿拉伯数字
3. 根据format参数返回相应格式
--]]
function convert_chinese_time(chinese_time, format)
    -- 参数检查
    if not chinese_time or type(chinese_time) ~= "string" or chinese_time == "" then
        return nil
    end

    -- 设置默认格式
    format = format or "hms"

    -- 初始化时间变量
    local hours = 0
    local minutes = 0
    local seconds = 0

    -- 提取小时
    local hour_match = string.match(chinese_time, "(.-)小时")
    if hour_match then
        hours = trans(hour_match) or 0
    end

    -- 提取分钟
    local minute_match = string.match(chinese_time, "(.-)分")
    if minute_match then
        -- 如果有小时，需要去掉小时部分再匹配分钟
        if hour_match then
            local after_hour = string.match(chinese_time, hour_match .. "小时(.*)")
            if after_hour then
                minute_match = string.match(after_hour, "(.-)分")
            end
        end
        minutes = trans(minute_match) or 0
    end

    -- 提取秒数
    local second_match = string.match(chinese_time, "(.-)秒")
    if second_match then
        -- 如果有分钟，需要去掉分钟部分再匹配秒数
        if minute_match then
            local after_minute = string.match(chinese_time, minute_match .. "分(.*)")
            if after_minute then
                second_match = string.match(after_minute, "(.-)秒")
            end
        elseif hour_match then
            -- 如果只有小时没有分钟，需要去掉小时部分再匹配秒数
            local after_hour = string.match(chinese_time, hour_match .. "小时(.*)")
            if after_hour then
                second_match = string.match(after_hour, "(.-)秒")
            end
        end
        seconds = trans(second_match) or 0
    end

    -- 计算总秒数
    local total_seconds = hours * 3600 + minutes * 60 + seconds

    -- 根据format参数返回相应格式
    if format == "seconds" or format == "s" then
        return total_seconds
    elseif format == "minutes" or format == "m" then
        return math.floor((total_seconds / 60) * 100) / 100   -- 保留两位小数
    elseif format == "hours" or format == "h" then
        return math.floor((total_seconds / 3600) * 100) / 100 -- 保留两位小数
    elseif format == "hm" then
        local h = math.floor(total_seconds / 3600)
        local m = math.floor((total_seconds % 3600) / 60)
        return h .. "小时" .. m .. "分"
    elseif format == "hms" then
        local h = math.floor(total_seconds / 3600)
        local m = math.floor((total_seconds % 3600) / 60)
        local s = total_seconds % 60
        return h .. "小时" .. m .. "分" .. s .. "秒"
    else
        -- 默认返回hms格式
        local h = math.floor(total_seconds / 3600)
        local m = math.floor((total_seconds % 3600) / 60)
        local s = total_seconds % 60
        return h .. "小时" .. m .. "分" .. s .. "秒"
    end
end

--[[
-- 用法示例：
-- 1. 转换为总秒数
local total_sec = convert_chinese_time("十九小时十八分三十六秒", "seconds")
print("总秒数：" .. total_sec)  -- 输出：总秒数：69516

-- 2. 转换为总分钟数
local total_min = convert_chinese_time("十九小时十八分三十六秒", "minutes")
print("总分钟数：" .. total_min)  -- 输出：总分钟数：1158.6

-- 3. 转换为总小时数
local total_hour = convert_chinese_time("十九小时十八分三十六秒", "hours")
print("总小时数：" .. total_hour)  -- 输出：总小时数：19.31

-- 4. 转换为小时分格式
local hm_format = convert_chinese_time("十九小时十八分三十六秒", "hm")
print("小时分格式：" .. hm_format)  -- 输出：小时分格式：19小时18分

-- 5. 转换为小时分秒格式（默认）
local hms_format = convert_chinese_time("十九小时十八分三十六秒")
print("小时分秒格式：" .. hms_format)  -- 输出：小时分秒格式：19小时18分36秒

-- 6. 处理只有分秒的情况
local min_sec = convert_chinese_time("三分二十秒", "seconds")
print("三分二十秒总秒数：" .. min_sec)  -- 输出：三分二十秒总秒数：200

-- 7. 处理只有秒的情况
local only_sec = convert_chinese_time("四十五秒", "minutes")
print("四十五秒总分钟数：" .. only_sec)  -- 输出：四十五秒总分钟数：0.75
--]]

-- =========================================================================
-- 辅助函数：将字符串转换为一个数字哈希值
-- =========================================================================
function getStringHash(str)
    local hash = 5381
    local mod = 1000000007
    for i = 1, #str do
        local char = string.byte(str, i)
        hash = ((hash * 33) + char) % mod
        -- 额外的混淆步骤，增加随机性
        hash = ((hash * 16777619) + char * i) % mod
    end
    return hash
end

-- =========================================================================
-- 声明缓存表和日期变量
-- =========================================================================
local timestampCache = {}
local cacheDate = nil -- 初始化为 nil 或者一个过去的时间

-- =========================================================================
-- 检查和重置缓存的函数
-- =========================================================================
local function checkAndResetCacheOnDateChange()
    local todayDateString = os.date("%Y-%m-%d")
    -- 如果 cacheDate 还没有被设置，或者已经不是今天了
    if not cacheDate or todayDateString ~= cacheDate then
        -- 清空缓存并更新日期记录
        timestampCache = {}
        cacheDate = todayDateString
        -- (可选) 可以在这里打印一条日志，方便调试，例如：
        -- echo("日期已更新至 " .. cacheDate .. ", 缓存已重置。")
    end
end

-- =========================================================================
-- 核心计算函数
-- =========================================================================
function getTriggerTimestamp(userIdentifier, taskIdentifier, startHour, endHour)
    -- *** 每次调用时，都先执行日期检查 ***
    checkAndResetCacheOnDateChange()

    if startHour >= endHour or not userIdentifier or not taskIdentifier then
        return nil
    end

    -- 这里的 todayDateString 可以直接使用全局的 cacheDate，因为它刚刚被更新过
    local seedString = userIdentifier .. ":" .. taskIdentifier .. ":" .. cacheDate

    -- 调试输出
    -- echo("DEBUG: seedString = " .. seedString)

    -- 先检查缓存
    if timestampCache[seedString] then
        -- echo("DEBUG: 使用缓存结果")
        return timestampCache[seedString]
    end

    local hash = getStringHash(seedString)
    -- echo("DEBUG: hash = " .. hash)
    local windowDurationSeconds = (endHour - startHour) * 3600
    if windowDurationSeconds <= 0 then return nil end
    local randomOffsetSeconds = math.fmod(math.abs(hash), windowDurationSeconds)
    local currentDateInfo = os.date("*t")
    local windowStartTimestamp = os.time({
        year = currentDateInfo.year,
        month = currentDateInfo.month,
        day = currentDateInfo.day,
        hour = startHour,
        min = 0,
        sec = 0
    })
    local finalTimestamp = windowStartTimestamp + randomOffsetSeconds
    timestampCache[seedString] = finalTimestamp
    return finalTimestamp
end

-- =========================================================================
-- 核心业务函数
-- =========================================================================
function isMyTimeReady(userIdentifier, taskIdentifier, providedTimestamp, startHour, endHour)
    startHour = startHour or 2
    endHour = endHour or 20

    if not userIdentifier or not taskIdentifier then return false end

    local now = os.time()
    local currentDateInfo = os.date("*t", now)

    if not providedTimestamp or providedTimestamp == 0 then return false end

    local providedDateInfo = os.date("*t", providedTimestamp)
    if (providedDateInfo.year == currentDateInfo.year and
            providedDateInfo.month == currentDateInfo.month and
            providedDateInfo.day == currentDateInfo.day) then
        return false
    end

    local deadlineHour = math.min(endHour + 1, 24)
    if currentDateInfo.hour >= deadlineHour then
        return false
    end

    local triggerTimestamp = getTriggerTimestamp(userIdentifier, taskIdentifier, startHour, endHour)
    if not triggerTimestamp then
        return false
    end

    return now >= triggerTimestamp
end

-- =========================================================================
-- 调用 isMyTimeReady 进行完整模拟
-- =========================================================================
add_alias("testmytime", function(params)
    if type(params) ~= "table" then
        echo("错误：脚本接收到的参数格式不正确。")
        return
    end

    -- 解析所有可能的参数
    local userIdentifier = params[1]
    local taskIdentifier = params[2]
    -- 提供默认值，与 isMyTimeReady 内部逻辑完全一致
    local startHour = tonumber(params[3]) or 2
    local endHour = tonumber(params[4]) or 20
    -- 第五个参数是可选的 providedTimestamp
    local providedTimestamp = tonumber(params[5])

    if not userIdentifier or userIdentifier == "" or not taskIdentifier or taskIdentifier == "" then
        -- 如果玩家ID是nil 或者 是空字符串，或者任务ID是nil 或者 是空字符串，那么...
        echo("用法: testmytime <玩家ID> <任务ID> [开始小时] [结束小时] [上次完成时间戳]")
        echo("示例1 (查询计算时间): testmytime gmnewn qinghuaimeng 0 2")
        echo("示例2 (完整模拟): testmytime gmnewn qinghuaimeng 2 20 1721232000")
        echo("示例3 (任务ID可选): battleship qinghuaimeng challenge ttt")
        return  -- 立即退出函数
    end

    -- === 模拟诊断 ===

    -- 如果用户没有提供时间戳，我们就伪造一个“昨天”的，以通过检查
    -- 这使得命令既能用于查询时间，也能用于完整模拟
    local timestampForSim = providedTimestamp or (os.time() - 86400)

    -- 1. 执行真正的业务函数
    local can_enter = isMyTimeReady(userIdentifier, taskIdentifier, timestampForSim, startHour, endHour)

    -- 2. 获取用于诊断的详细信息
    local trigger_time = getTriggerTimestamp(userIdentifier, taskIdentifier, startHour, endHour)
    local deadlineHour = math.min(endHour + 1, 24)
    local now_time = os.time()

    -- 3. 打印详细的诊断报告
    echo("\n==================================================")
    echo("          isMyTimeReady 函数模拟器")
    echo("==================================================")
    echo("输入参数:")
    echo("  - 玩家ID: " .. userIdentifier)
    echo("  - 任务ID: " .. taskIdentifier)
    echo("  - 时间窗口: " .. startHour .. ":00 - " .. endHour .. ":00")
    echo("  - 用于模拟的时间戳: " .. timestampForSim .. " (" .. os.date('%Y-%m-%d %H:%M:%S', timestampForSim) .. ")")
    if not providedTimestamp then
        echo("    (提示: 未提供时间戳，已自动使用昨天的时间以绕过日期检查)")
    end
    echo("--------------------------------------------------")
    echo("诊断信息:")
    echo("  - 专属开启时间: " .. (trigger_time and os.date('%H:%M:%S', trigger_time) or "计算失败"))
    echo("  - 最终截止时间: " .. deadlineHour .. ":00")
    echo("  - 当前服务器时间: " .. os.date('%H:%M:%S', now_time))
    echo("--------------------------------------------------")
    echo("最终模拟结果: " .. (can_enter and "[[[ 可以进入 (True) ]]]" or "[[[ 不能进入 (False) ]]]"))
    echo("==================================================")
end)

-- ===================================================================
-- 倒仓库脚本
-- ===================================================================

local warehouseItems = {}
local warehousePattern1 = "┣━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┫"
local warehousePattern2 = "^\\s*┃\\s*(.*?)\\s{2,}([^\\s]+.*?)"

-- ===================================================================
--                          核心功能模块
-- ===================================================================

add_trigger("DaoCangKu_Item_Capture_1", warehousePattern1, function(params)
    add_trigger("DaoCangKu_Item_Capture_2", warehousePattern2, function(params)
        local itemId = params[1]
        local itemName = params[2]

        -- 过滤掉表头行，避免抓取标题行
        if itemId and itemName and not string.find(itemId, "ID") and not string.find(itemName, "货物") then
            -- 去除物品名称的前后空格
            itemName = string.gsub(itemName, "^%s*(.-)%s*$", "%1")
            --echo("【物品捕获】物品ID: " .. itemId .. ", 物品名称: " .. itemName)
            if not warehouseItems[itemId] then
                warehouseItems[itemId] = { id = itemId, name = itemName, quantity = 0 }
            end
            warehouseItems[itemId].quantity = warehouseItems[itemId].quantity + 1
        end
    end)
end)

-- 【2. 倒仓库主函数】
function DaoCangKu(playerName, filterItemName)
    if not playerName or playerName == "" then
        echo("【用法错误】请提供一个玩家名字。格式: daocangku '玩家ID' [物品名称]")
        return
    end
    echo("===================================================")
    if filterItemName and filterItemName ~= "" then
        echo("【系统】倒仓库流程启动，目标玩家: " .. playerName .. "，过滤物品: " .. filterItemName)
    else
        echo("【系统】倒仓库流程启动，目标玩家: " .. playerName .. "（全部物品）")
    end
    echo("===================================================")
    echo("【步骤 1/2】正在清空缓存并发送 'dlist' 命令...")
    warehouseItems = {}


    b(function()
        local itemsToGive = {}
        for id, data in pairs(warehouseItems) do
            -- 如果指定了过滤物品名称，则只处理匹配的物品
            local isMatch = false
            if not filterItemName or filterItemName == "" then
                isMatch = true
            else
                -- 去除两边空格后进行包含匹配
                local cleanDataId = string.gsub(data.id, "^%s*(.-)%s*$", "%1")
                local cleanFilterName = string.gsub(filterItemName, "^%s*(.-)%s*$", "%1")

                -- 使用ID进行匹配
                isMatch = string.find(cleanDataId, cleanFilterName, 1, true) ~= nil
            end

            if isMatch then
                for i = 1, data.quantity do
                    table.insert(itemsToGive, { id = data.id, name = data.name })
                end
            end
        end

        echo("---------------------------------------------------")
        if filterItemName and filterItemName ~= "" then
            echo("【步骤 2/2】仓库扫描完成，过滤物品 '" .. filterItemName .. "'，共捕获到 " .. #itemsToGive .. " 件物品。")
        else
            echo("【步骤 2/2】仓库扫描完成，共捕获到 " .. #itemsToGive .. " 件物品。")
        end
        echo("---------------------------------------------------")
        if #itemsToGive == 0 then
            echo("【流程结束】未能捕获到任何物品。")
            return
        end

        local function processNextItem()
            if #itemsToGive == 0 then
                echo("---------------------------------------------------")
                echo("【全部完成】所有物品均已尝试处理！流程结束。")
                echo("===================================================")
                return
            end
            close_triggers("DaoCangKu_Item_Capture", 1, 3)
            local currentItem = itemsToGive[1]
            echo("--> 正在处理: " .. currentItem.name .. " | 剩余: " .. (#itemsToGive - 1) .. "件")
            send("qu " .. currentItem.id)

            b(function()
                send("alias action 取出来了吗？")
                add_trigger("DaoCangKu_Item_Capture_3", "你把 \"action\" 设定为 \"取出来了吗？\" 成功完成。", function(params)
                    table.remove(itemsToGive, 1)
                    send("give " .. playerName .. " " .. currentItem.id)
                    b(function()
                        -- give完成后，递归调用自己，处理下一个物品
                        processNextItem()
                    end)
                end)
            end)
        end
        processNextItem()
    end)
end

close_triggers("DaoCangKu_Item_Capture", 1, 3)
-- ===================================================================
--                          别名设置
-- ===================================================================
add_alias("daocangku", function(params)
    -- 检查参数是否有效
    if params == nil or #params == 0 or params[1] == nil or params[1] == "" or string.len(tostring(params[1])) == 0 then
        echo(C.R.."【用法错误】请提供一个玩家名字。格式: daocangku 玩家ID 物品名称")
        echo(C.R.."【用法实例】daocangku zhangsan".." 这是把仓库里的所有东西都取出来给zhangsan")
        echo(C.R.."【用法示例】daocangku zhangsan fengling yu".." 这是把仓库里的所有fengling yu都取出来给zhangsan")
        return
    end
    
    g(80, function()
        open_triggers("DaoCangKu_Item_Capture", 1, 3)        
        send("dlist")
        -- 第一个参数是玩家名
        local playerName = params[1]
        local filterItemName = nil
        if params[2] then
            -- 如果有第二个参数，将从第二个参数开始的所有参数用空格连接
            local itemParts = {}
            for i = 2, #params do
                table.insert(itemParts, params[i])
            end
            filterItemName = table.concat(itemParts, " ")
            -- 去除前后空格
            filterItemName = string.gsub(filterItemName, "^%s*(.-)%s*$", "%1")
        end
        DaoCangKu(playerName, filterItemName)
    end)
end)


---
-- 在包含 ANSI 颜色代码的文本中，查找并替换子字符串。
-- 通过 is_plain_text 参数控制是进行纯文本查找还是模式查找。
-- @param raw_text 带有颜色代码的原始文本
-- @param find_target 要查找的【纯文本】或【模式】
-- @param replace_with 用于替换的文本
-- @param is_plain_text (布尔值, 可选) 如果为 true，则将 find_target 视为纯文本；
--                      如果为 false 或 nil（默认），则将其视为模式。
-- @return string 处理后的文本
---
function replace(raw_text, find_target, replace_with, is_plain_text)
    if find_target == nil or find_target == "" then
        return raw_text
    end

    local clean_text = string.gsub(raw_text, "\x1b%[.-m", "")

    -- 根据 is_plain_text 参数决定如何查找
    local clean_start, clean_end
    if is_plain_text then
        -- 以纯文本方式查找，true 参数会关闭模式匹配的“魔法”
        clean_start, clean_end = string.find(clean_text, find_target, 1, true)
    else
        -- 以模式方式查找（默认行为）
        clean_start, clean_end = string.find(clean_text, find_target)
    end

    if not clean_start then
        return raw_text
    end

    local raw_start_index, raw_end_index = -1, -1
    local visible_chars_count = 0
    local i = 1
    while i <= #raw_text do
        local ansi_match_end = string.find(raw_text, "m", i, true)
        if string.sub(raw_text, i, i) == "\x1b" and ansi_match_end then
            i = ansi_match_end + 1
        else
            visible_chars_count = visible_chars_count + 1
            if visible_chars_count == clean_start and raw_start_index == -1 then
                raw_start_index = i
            end
            if visible_chars_count == clean_end and raw_end_index == -1 then
                raw_end_index = i
                break
            end
            i = i + 1
        end
    end

    if raw_start_index ~= -1 and raw_end_index ~= -1 then
        local part_before = string.sub(raw_text, 1, raw_start_index - 1)
        local part_after = string.sub(raw_text, raw_end_index + 1)
        return part_before .. (replace_with or "") .. part_after
    end

    return raw_text
end
-- 模块加载完成提示
Print("--- 加载模块: 通用函数 ---")